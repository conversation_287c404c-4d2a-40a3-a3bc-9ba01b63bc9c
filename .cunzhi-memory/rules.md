# 开发规范和规则

- 用户要求在开始实施前必须深入了解所有官方文档，确定具体的实现方案、前端设计要求、后端接入细节和功能优先级，不能匆忙开始编码
- 用户明确要求在开始任何实施前必须获得明确许可，不能自行开始编码。用户强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户要求测试完API连接后，记住要配置环境变量以提高安全性，将硬编码的API密钥移到环境变量中
- 不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行，用户自己处理这些
- 用户严重质疑AI的分析，要求不允许乱分析乱改，强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户对AI的修改非常愤怒，指出协作计划的支出不会统计到首页，数据流被反复更改导致问题，要求立即修复
- 协作支出统计修复：首页财务概览必须显示本月数据，协作支出查询需要包含用户创建的和参与的所有协作计划，数据去重使用Map按_id去重，缓存清理需要通知所有协作者
- 项目重影问题已全面修复：1.统一所有毛玻璃效果为blur(24rpx) saturate(180%)；2.删除重复样式文件theme.wxss和common.wxss；3.修复transform冲突，添加translateZ(0)硬件加速；4.创建anti-ghosting-fixes.wxss专用修复文件；5.优化字体渲染，添加-webkit-font-smoothing属性；6.修复所有CSS语法错误
- 用户明确要求确保项目改造的可行性，深入了解开发，深入学习官方文档以及网络上的相关信息。用户强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户确认开始实施圆周旅迹式改造方案，要求学习圆周旅记的前端交互布局，使用小程序的特色配色。用户强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户要求对旅行规划页面进行重新设计，需要进一步优化。用户强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户询问为什么点击地图会跳转到详情页面，需要优化地图功能的跳转逻辑。用户强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户要求解决问题而不是避免问题，在今后的所有问题中都要这样处理。用户强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户明确禁止使用降级方案，要求正式解决问题而不是逃避问题，必须查阅资料解决根本问题。用户强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户明确要求：不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 微信小程序插件版本问题修复：腾讯位置服务路线规划插件wx50b5593e81dd937a版本从2.0.6降级到1.0.3，因为2.0.6版本不存在且插件已下架，1.0.3是官方支持的最新版本
- 微信小程序所有插件版本问题全面修复：1.路线规划插件wx50b5593e81dd937a从2.0.6降级到1.0.3；2.城市选择器插件wx63ffb7b7894e99ae从1.0.4降级到1.0.2；3.地图选点插件wx76a9a06e5b4e693e从1.1.2降级到1.0.12，所有版本均为官方支持的最新稳定版本
- 微信小程序插件版本最终更新：1.路线规划插件wx50b5593e81dd937a更新到2.0.5版本，恢复多种出行方式和路线策略功能；2.城市选择器插件wx63ffb7b7894e99ae更新到1.0.3版本；3.地图选点插件wx76a9a06e5b4e693e更新到1.1.1版本，所有插件均使用最新稳定版本
- 头像管理系统重构完成：移除复杂的avatar-cache-manager.js缓存系统，改为直接调用云函数travel.uploadAvatar处理头像上传，解决了chooseAvatar临时文件路径错误问题，简化了架构并符合微信官方最佳实践
- JavaScript错误修复完成：1.修复setupRealtimeSync方法名为setupDataSync；2.将所有dataManager引用替换为simpleDataManager；3.使用本地缓存方式替代不存在的dataManager.getTravelPlans方法；4.保持现有数据流架构，确保最小化云函数调用和数据一致性
