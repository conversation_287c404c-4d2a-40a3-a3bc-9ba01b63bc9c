/**
 * AI旅行云函数
 * 处理智能旅行规划相关的所有AI服务调用
 */

const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const ZhipuAIService = require('./services/zhipu-ai')
const BaiduNLPService = require('./services/baidu-nlp')
const QWeatherService = require('./services/qweather')
const WebCrawlerService = require('./services/web-crawler')
const RouteOptimizer = require('./lib/RouteOptimizer')

// 从环境变量获取API密钥（优先使用环境变量，提高安全性）
const ZHIPU_API_KEY = process.env.ZHIPU_API_KEY
const BAIDU_APP_ID = process.env.BAIDU_APP_ID
const BAIDU_API_KEY = process.env.BAIDU_API_KEY
const BAIDU_SECRET_KEY = process.env.BAIDU_SECRET_KEY

// 和风天气JWT配置（使用固定配置，避免环境变量换行问题）
const QWEATHER_KEY_ID = process.env.QWEATHER_KEY_ID || 'CFB4NPJKBG'
const QWEATHER_PROJECT_ID = process.env.QWEATHER_PROJECT_ID || '4JDXG7XUJ2'
const QWEATHER_PRIVATE_KEY = `-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIOt8XWOrzvLvRkTvLJk5kX71tMR2uWB5t3jogFs4A3fO
-----END PRIVATE KEY-----`

// 初始化服务
const zhipuService = new ZhipuAIService(ZHIPU_API_KEY)
const baiduService = new BaiduNLPService(BAIDU_APP_ID, BAIDU_API_KEY, BAIDU_SECRET_KEY)
const qweatherService = new QWeatherService(QWEATHER_KEY_ID, QWEATHER_PROJECT_ID, QWEATHER_PRIVATE_KEY)
const crawlerService = new WebCrawlerService()

exports.main = async (event, context) => {
  const { action, data } = event
  
  console.log('AI Travel Function Called:', { action, data })
  
  try {
    switch (action) {
      case 'parseTravel':
        return await parseTravel(data.text)

      case 'generateItinerary':
        return await generateItinerary(data.requirements)

      case 'extractLocations':
        return await extractLocations(data.text)

      case 'getWeather':
        return await getWeather(data.location, data.date)

      case 'optimizeRoute':
        return await optimizeRoute(data.locations)

      case 'smartPlanning':
        return await smartPlanning(data.destination, data.duration, data.preferences)

      case 'getAttractions':
        return await getAttractions(data.destination, data.preferences)

      case 'generateMapData':
        return await generateMapData(data.attractions, data.routes)

      case 'testConnection':
        return await testConnection()

      case 'magicParse':
        return await magicParse(data.url)

      case 'parseAndExtract':
        return await parseAndExtract(data.url)

      default:
        return {
          success: false,
          message: `未知操作: ${action}`
        }
    }
  } catch (error) {
    console.error('AI Travel Function Error:', error)
    return { 
      success: false, 
      message: error.message,
      error: error.stack 
    }
  }
}

/**
 * 解析旅行攻略文本
 */
async function parseTravel(text) {
  try {
    // 使用智谱AI解析旅行攻略
    const aiResult = await zhipuService.parseTravel(text)
    
    // 使用百度NLP提取地址信息
    const locations = await baiduService.extractAddresses(text)
    
    return {
      success: true,
      data: {
        aiParsed: aiResult,
        locations: locations,
        originalText: text,
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('Parse travel error:', error)
    return {
      success: false,
      message: '解析旅行攻略失败',
      error: error.message
    }
  }
}

/**
 * 智能生成行程
 */
async function generateItinerary(requirements) {
  try {
    const itinerary = await zhipuService.generateItinerary(requirements)
    
    return {
      success: true,
      data: {
        itinerary: itinerary,
        requirements: requirements,
        generatedAt: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('Generate itinerary error:', error)
    return {
      success: false,
      message: '生成行程失败',
      error: error.message
    }
  }
}

/**
 * 提取地点信息
 */
async function extractLocations(text) {
  try {
    const locations = await baiduService.extractAddresses(text)
    
    return {
      success: true,
      data: {
        locations: locations,
        count: locations.length
      }
    }
  } catch (error) {
    console.error('Extract locations error:', error)
    return {
      success: false,
      message: '提取地点信息失败',
      error: error.message
    }
  }
}

/**
 * 获取天气信息
 */
async function getWeather(location, date) {
  try {
    if (!QWEATHER_KEY_ID || !QWEATHER_PROJECT_ID || !QWEATHER_PRIVATE_KEY) {
      return {
        success: false,
        message: '和风天气配置未完成，请联系管理员'
      }
    }
    
    const weather = await qweatherService.getWeather(location, date)
    
    return {
      success: true,
      data: weather
    }
  } catch (error) {
    console.error('Get weather error:', error)
    return {
      success: false,
      message: '获取天气信息失败',
      error: error.message
    }
  }
}

/**
 * 优化路线 - 使用多算法融合的智能优化系统
 */
async function optimizeRoute(locations, preferences = {}) {
  try {
    console.log('开始智能路线优化，景点数量:', locations.length)

    if (!locations || locations.length < 2) {
      return {
        success: false,
        message: '景点数量不足，无法进行路线优化'
      }
    }

    // 验证景点数据格式
    const validatedLocations = locations.map((location, index) => {
      if (!location.coordinates || !location.coordinates.latitude || !location.coordinates.longitude) {
        throw new Error(`景点 ${index} 缺少有效的坐标信息`)
      }
      return {
        ...location,
        id: location.id || `location_${index}`,
        name: location.name || `景点${index + 1}`,
        type: location.type || 'attraction',
        ticketPrice: parseFloat(location.ticketPrice) || 0,
        estimatedDuration: parseInt(location.estimatedDuration) || 120,
        openTime: location.openTime || '09:00',
        closeTime: location.closeTime || '17:00'
      }
    })

    // 创建路线优化器实例
    const optimizer = new RouteOptimizer({
      zhipuService: zhipuService,
      genetic: {
        populationSize: Math.min(50, Math.max(20, validatedLocations.length * 2)),
        generations: Math.min(100, Math.max(50, validatedLocations.length * 5)),
        timeout: 300000 // 5分钟超时
      },
      simulated: {
        maxIterations: Math.min(1000, validatedLocations.length * 50),
        timeout: 300000
      },
      antColony: {
        antCount: Math.min(30, Math.max(10, validatedLocations.length)),
        iterations: Math.min(100, Math.max(30, validatedLocations.length * 3)),
        timeout: 300000
      }
    })

    // 执行智能优化
    const optimizationResult = await optimizer.optimize(validatedLocations, preferences)

    if (!optimizationResult.success) {
      throw new Error(optimizationResult.message)
    }

    // 构建返回数据
    const optimizedData = optimizationResult.data
    const optimizedLocations = optimizedData.optimizedRoute.map(index => validatedLocations[index])

    return {
      success: true,
      data: {
        originalOrder: validatedLocations,
        optimizedOrder: optimizedLocations,
        optimizedRoute: optimizedData.optimizedRoute,
        totalDistance: optimizedData.totalDistance,
        totalTime: optimizedData.totalTime,
        optimizationScore: optimizedData.optimizationScore,
        algorithmUsed: optimizedData.algorithmUsed,
        aiEnhanced: optimizedData.aiEnhanced,
        aiAnalysis: optimizedData.aiAnalysis,
        optimizedAt: optimizedData.optimizedAt,
        preferences: preferences
      }
    }

  } catch (error) {
    console.error('智能路线优化失败:', error)
    return {
      success: false,
      message: error.message || '路线优化失败',
      error: error.stack
    }
  }
}

/**
 * 测试连接
 */
async function testConnection() {
  const results = {
    zhipu: false,
    baidu: false,
    qweather: false
  }
  
  try {
    // 测试智谱AI连接
    await zhipuService.testConnection()
    results.zhipu = true
  } catch (error) {
    console.error('Zhipu connection test failed:', error)
  }
  
  try {
    // 测试百度NLP连接
    await baiduService.testConnection()
    results.baidu = true
  } catch (error) {
    console.error('Baidu connection test failed:', error)
  }
  
  try {
    // 测试和风天气连接
    if (QWEATHER_KEY_ID && QWEATHER_PROJECT_ID && QWEATHER_PRIVATE_KEY) {
      await qweatherService.testConnection()
      results.qweather = true
    }
  } catch (error) {
    console.error('QWeather connection test failed:', error)
  }
  
  return {
    success: true,
    data: {
      connections: results,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * 智能规划行程
 * @param {string} destination 目的地
 * @param {number} duration 天数
 * @param {array} preferences 偏好设置
 */
async function smartPlanning(destination, duration, preferences = []) {
  try {
    console.log('开始智能规划:', { destination, duration, preferences })

    // 1. 获取目的地景点推荐
    const attractionsResult = await getAttractions(destination, preferences)
    if (!attractionsResult.success) {
      throw new Error('获取景点推荐失败: ' + attractionsResult.message)
    }

    // 2. 基于天数和偏好筛选景点
    const selectedAttractions = selectAttractionsByDuration(
      attractionsResult.data.attractions,
      duration,
      preferences
    )

    // 3. 优化游览路径（传递用户偏好）
    const routePreferences = {
      preferShortest: preferences.includes('经济实惠'),
      preferFastest: preferences.includes('时间优先'),
      preferredTypes: preferences,
      budgetConscious: preferences.includes('经济实惠') ? 0.8 : 0.3,
      energyLevel: preferences.includes('轻松游') ? 2 : preferences.includes('深度游') ? 4 : 3
    }

    const routeResult = await optimizeRoute(selectedAttractions, routePreferences)
    if (!routeResult.success) {
      throw new Error('路径优化失败: ' + routeResult.message)
    }

    // 4. 生成每日行程安排
    const dailyPlan = generateDailyPlan(selectedAttractions, duration)

    // 5. 计算预估费用
    const estimatedCost = calculateEstimatedCost(selectedAttractions, duration)

    // 6. 生成地图数据
    const mapDataResult = await generateMapData(selectedAttractions, routeResult.data.optimizedOrder)

    // 7. 生成行程标题
    const title = generateTripTitle(destination, duration, preferences)

    return {
      success: true,
      data: {
        title: title,
        destination: destination,
        duration: duration,
        preferences: preferences,
        attractions: selectedAttractions,
        dailyPlan: dailyPlan,
        routes: routeResult.data.optimizedOrder,
        optimizedRoute: routeResult.data.optimizedRoute,
        routeOptimization: {
          totalDistance: routeResult.data.totalDistance,
          totalTime: routeResult.data.totalTime,
          algorithmUsed: routeResult.data.algorithmUsed,
          optimizationScore: routeResult.data.optimizationScore,
          aiEnhanced: routeResult.data.aiEnhanced,
          aiAnalysis: routeResult.data.aiAnalysis
        },
        mapData: mapDataResult.success ? mapDataResult.data : null,
        estimatedCost: estimatedCost,
        totalDistance: routeResult.data.totalDistance || calculateTotalDistance(selectedAttractions),
        generatedAt: new Date().toISOString()
      }
    }

  } catch (error) {
    console.error('智能规划失败:', error)
    return {
      success: false,
      message: error.message || '智能规划失败',
      error: error.message
    }
  }
}

/**
 * 获取景点推荐
 * @param {string} destination 目的地
 * @param {array} preferences 偏好设置
 */
async function getAttractions(destination, preferences = []) {
  try {
    // 构建景点推荐提示词
    const prompt = `
请为${destination}推荐旅游景点，用户偏好：${preferences.join('、')}

要求：
1. 推荐10-15个景点
2. 包含不同类型：${preferences.includes('culture') ? '文化历史、' : ''}${preferences.includes('nature') ? '自然风光、' : ''}${preferences.includes('food') ? '美食体验、' : ''}${preferences.includes('shopping') ? '购物娱乐、' : ''}经典必游
3. 提供详细信息：名称、类型、推荐理由、预估游览时间、门票价格
4. 按推荐优先级排序

返回JSON格式：
{
  "attractions": [
    {
      "id": "unique_id",
      "name": "景点名称",
      "type": "culture/nature/food/shopping/classic",
      "description": "景点描述",
      "reason": "推荐理由",
      "estimatedDuration": 120,
      "ticketPrice": 50,
      "rating": 4.5,
      "address": "详细地址",
      "coordinates": {
        "latitude": 39.9042,
        "longitude": 116.4074
      },
      "openTime": "09:00-17:00",
      "tips": ["游览建议1", "游览建议2"]
    }
  ]
}
`

    // 调用智谱AI获取景点推荐
    const aiResult = await zhipuService.callAPI('/chat/completions', {
      model: 'glm-4-flash',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的旅游规划师，擅长根据用户偏好推荐合适的景点。请严格按照JSON格式返回结果。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    })

    if (!aiResult || !aiResult.choices || aiResult.choices.length === 0) {
      throw new Error('AI响应格式异常')
    }

    // 解析AI返回的JSON
    const responseText = aiResult.choices[0].message.content
    console.log('AI原始响应:', responseText)

    let attractionsData
    try {
      const cleanResponse = responseText
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()

      attractionsData = JSON.parse(cleanResponse)
    } catch (parseError) {
      console.error('JSON解析失败:', parseError)
      console.error('原始响应:', responseText)

      // 如果JSON解析失败，返回默认数据
      attractionsData = {
        attractions: [{
          id: 'default_1',
          name: destination,
          type: 'classic',
          description: `${destination}旅游景点`,
          reason: '热门目的地',
          estimatedDuration: 180,
          ticketPrice: 0,
          rating: 4.0,
          address: destination,
          coordinates: null,
          openTime: '09:00-17:00',
          tips: ['建议提前规划路线']
        }]
      }
    }

    // 验证和补充数据
    const validatedAttractions = (attractionsData.attractions || []).map((attraction, index) => ({
      id: attraction.id || `attraction_${index}`,
      name: attraction.name || '未知景点',
      type: attraction.type || 'classic',
      description: attraction.description || '',
      reason: attraction.reason || '',
      estimatedDuration: parseInt(attraction.estimatedDuration) || 120,
      ticketPrice: parseFloat(attraction.ticketPrice) || 0,
      rating: parseFloat(attraction.rating) || 4.0,
      address: attraction.address || '',
      coordinates: attraction.coordinates || null,
      openTime: attraction.openTime || '09:00-17:00',
      tips: Array.isArray(attraction.tips) ? attraction.tips : []
    }))

    return {
      success: true,
      data: {
        destination: destination,
        preferences: preferences,
        attractions: validatedAttractions,
        total: validatedAttractions.length
      }
    }

  } catch (error) {
    console.error('获取景点推荐失败:', error)
    return {
      success: false,
      message: '获取景点推荐失败',
      error: error.message
    }
  }
}

/**
 * 生成地图数据
 * @param {array} attractions 景点列表
 * @param {array} routes 路线数据
 */
async function generateMapData(attractions, routes = []) {
  try {
    if (!attractions || attractions.length === 0) {
      throw new Error('景点数据为空')
    }

    // 计算地图中心点
    const validCoordinates = attractions
      .filter(attr => attr.coordinates && attr.coordinates.latitude && attr.coordinates.longitude)

    if (validCoordinates.length === 0) {
      throw new Error('没有有效的坐标数据')
    }

    const centerLat = validCoordinates.reduce((sum, attr) => sum + attr.coordinates.latitude, 0) / validCoordinates.length
    const centerLng = validCoordinates.reduce((sum, attr) => sum + attr.coordinates.longitude, 0) / validCoordinates.length

    // 生成地图标记
    const markers = validCoordinates.map((attraction, index) => ({
      id: index, // 确保ID是数字
      latitude: attraction.coordinates.latitude,
      longitude: attraction.coordinates.longitude,
      title: attraction.name,
      width: 30,
      height: 30,
      callout: {
        content: attraction.name,
        color: '#333',
        fontSize: 12,
        borderRadius: 4,
        bgColor: '#fff',
        padding: 8,
        display: 'BYCLICK'
      }
    }))

    // 生成路线数据
    const polylines = validCoordinates.length > 1 ? [{
      points: validCoordinates.map(attr => ({
        latitude: attr.coordinates.latitude,
        longitude: attr.coordinates.longitude
      })),
      color: '#FF6B6B',
      width: 4,
      dottedLine: false,
      arrowLine: true,
      borderColor: '#FF8E8E',
      borderWidth: 2
    }] : []

    // 计算合适的缩放级别
    const scale = calculateMapScale(validCoordinates)

    return {
      success: true,
      data: {
        center: {
          latitude: centerLat,
          longitude: centerLng
        },
        markers: markers,
        polyline: polylines,
        scale: scale,
        attractionCount: validCoordinates.length,
        routeCount: polylines.length
      }
    }

  } catch (error) {
    console.error('生成地图数据失败:', error)
    return {
      success: false,
      message: '生成地图数据失败',
      error: error.message
    }
  }
}

/**
 * 根据天数和偏好筛选景点
 */
function selectAttractionsByDuration(attractions, duration, preferences) {
  // 每天推荐2-4个景点
  const maxAttractions = duration * 3

  // 按偏好和评分排序
  const sortedAttractions = attractions.sort((a, b) => {
    // 偏好匹配度
    const aPreferenceScore = preferences.includes(a.type) ? 2 : 0
    const bPreferenceScore = preferences.includes(b.type) ? 2 : 0

    // 综合评分
    const aScore = aPreferenceScore + (a.rating || 4.0)
    const bScore = bPreferenceScore + (b.rating || 4.0)

    return bScore - aScore
  })

  return sortedAttractions.slice(0, maxAttractions)
}

/**
 * 生成每日行程安排
 */
function generateDailyPlan(attractions, duration) {
  const dailyPlan = []
  const attractionsPerDay = Math.ceil(attractions.length / duration)

  for (let day = 1; day <= duration; day++) {
    const startIndex = (day - 1) * attractionsPerDay
    const endIndex = Math.min(startIndex + attractionsPerDay, attractions.length)
    const dayAttractions = attractions.slice(startIndex, endIndex)

    dailyPlan.push({
      day: day,
      date: null, // 将在前端设置具体日期
      attractions: dayAttractions.map((attraction, index) => ({
        ...attraction,
        order: index + 1,
        startTime: calculateStartTime(index),
        endTime: calculateEndTime(index, attraction.estimatedDuration)
      })),
      totalDuration: dayAttractions.reduce((sum, attr) => sum + (attr.estimatedDuration || 120), 0),
      totalCost: dayAttractions.reduce((sum, attr) => sum + (attr.ticketPrice || 0), 0)
    })
  }

  return dailyPlan
}

/**
 * 计算预估费用
 */
function calculateEstimatedCost(attractions, duration) {
  const ticketCost = attractions.reduce((sum, attr) => sum + (attr.ticketPrice || 0), 0)
  const mealCost = duration * 150 // 每天餐费150元
  const transportCost = duration * 50 // 每天交通费50元
  const accommodationCost = (duration - 1) * 200 // 每晚住宿200元

  return {
    tickets: ticketCost,
    meals: mealCost,
    transport: transportCost,
    accommodation: accommodationCost,
    total: ticketCost + mealCost + transportCost + accommodationCost
  }
}

/**
 * 生成行程标题
 */
function generateTripTitle(destination, duration, preferences) {
  const preferenceTexts = {
    culture: '文化',
    nature: '自然',
    food: '美食',
    shopping: '购物'
  }

  const preferenceStr = preferences
    .map(pref => preferenceTexts[pref])
    .filter(Boolean)
    .join('·')

  if (preferenceStr) {
    return `${destination}${duration}日${preferenceStr}之旅`
  } else {
    return `${destination}${duration}日精品游`
  }
}

/**
 * 获取景点图标（移除不存在的图标路径）
 */
function getAttractionIcon(type) {
  // 暂时不使用自定义图标，使用默认标记
  return null
}

/**
 * 计算地图缩放级别
 */
function calculateMapScale(coordinates) {
  if (coordinates.length <= 1) return 13

  const lats = coordinates.map(coord => coord.coordinates.latitude)
  const lngs = coordinates.map(coord => coord.coordinates.longitude)

  const latRange = Math.max(...lats) - Math.min(...lats)
  const lngRange = Math.max(...lngs) - Math.min(...lngs)
  const maxRange = Math.max(latRange, lngRange)

  if (maxRange > 0.1) return 10
  if (maxRange > 0.05) return 12
  if (maxRange > 0.02) return 13
  if (maxRange > 0.01) return 14
  return 15
}

/**
 * 计算总距离
 */
function calculateTotalDistance(attractions) {
  if (!attractions || attractions.length < 2) return 0

  let totalDistance = 0
  for (let i = 0; i < attractions.length - 1; i++) {
    const current = attractions[i]
    const next = attractions[i + 1]

    if (current.coordinates && next.coordinates) {
      const distance = calculateDistance(
        current.coordinates.latitude,
        current.coordinates.longitude,
        next.coordinates.latitude,
        next.coordinates.longitude
      )
      totalDistance += distance
    }
  }

  return Math.round(totalDistance * 100) / 100 // 保留两位小数
}

/**
 * 计算两点间距离（公里）
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371 // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

/**
 * 计算开始时间
 */
function calculateStartTime(index) {
  const baseHour = 9 // 上午9点开始
  const hoursPerAttraction = 2.5 // 每个景点平均2.5小时

  const totalHours = baseHour + (index * hoursPerAttraction)
  const hour = Math.floor(totalHours) % 24
  const minute = Math.floor((totalHours % 1) * 60)

  return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
}

/**
 * 计算结束时间
 */
function calculateEndTime(index, duration) {
  const startTime = calculateStartTime(index)
  const [startHour, startMinute] = startTime.split(':').map(Number)

  const totalMinutes = startHour * 60 + startMinute + (duration || 120)
  const endHour = Math.floor(totalMinutes / 60) % 24
  const endMinute = totalMinutes % 60

  return `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`
}

/**
 * 生成模拟景点数据（避免AI调用超时）
 */
function generateMockAttractions(destination, duration, preferences) {
  const attractionTemplates = {
    culture: [
      { name: '博物馆', type: 'culture', ticketPrice: 30, estimatedDuration: 120 },
      { name: '古建筑群', type: 'culture', ticketPrice: 50, estimatedDuration: 180 },
      { name: '历史街区', type: 'culture', ticketPrice: 0, estimatedDuration: 90 }
    ],
    nature: [
      { name: '公园', type: 'nature', ticketPrice: 20, estimatedDuration: 150 },
      { name: '山景区', type: 'nature', ticketPrice: 80, estimatedDuration: 240 },
      { name: '湖泊', type: 'nature', ticketPrice: 40, estimatedDuration: 120 }
    ],
    food: [
      { name: '特色餐厅', type: 'food', ticketPrice: 100, estimatedDuration: 90 },
      { name: '小吃街', type: 'food', ticketPrice: 50, estimatedDuration: 120 },
      { name: '老字号', type: 'food', ticketPrice: 80, estimatedDuration: 60 }
    ],
    shopping: [
      { name: '购物中心', type: 'shopping', ticketPrice: 0, estimatedDuration: 180 },
      { name: '特产市场', type: 'shopping', ticketPrice: 0, estimatedDuration: 120 },
      { name: '商业街', type: 'shopping', ticketPrice: 0, estimatedDuration: 150 }
    ]
  }

  const defaultAttractions = [
    { name: '市中心广场', type: 'classic', ticketPrice: 0, estimatedDuration: 60 },
    { name: '观景台', type: 'classic', ticketPrice: 30, estimatedDuration: 90 },
    { name: '文化中心', type: 'classic', ticketPrice: 40, estimatedDuration: 120 }
  ]

  let attractions = [...defaultAttractions]

  // 根据偏好添加相应类型的景点
  preferences.forEach(pref => {
    if (attractionTemplates[pref]) {
      attractions = attractions.concat(attractionTemplates[pref])
    }
  })

  // 生成完整的景点数据
  const maxAttractions = duration * 3
  return attractions.slice(0, maxAttractions).map((attraction, index) => ({
    id: `attraction_${index}`,
    name: `${destination}${attraction.name}`,
    type: attraction.type,
    description: `${destination}著名的${attraction.name}`,
    reason: `推荐游览${destination}的${attraction.name}`,
    estimatedDuration: attraction.estimatedDuration,
    ticketPrice: attraction.ticketPrice,
    rating: 4.0 + Math.random() * 1.0,
    address: `${destination}市区`,
    coordinates: {
      latitude: 39.9042 + (Math.random() - 0.5) * 0.1,
      longitude: 116.4074 + (Math.random() - 0.5) * 0.1
    },
    openTime: '09:00-17:00',
    tips: [`游览${attraction.name}的最佳时间`, `注意${attraction.name}的开放时间`]
  }))
}

/**
 * 魔法解析 - 圆周旅迹核心功能
 * @param {string} url 链接地址
 */
async function magicParse(url) {
  try {
    console.log('开始魔法解析:', url)

    // 使用爬虫服务解析网页内容
    const crawlResult = await crawlerService.magicParse(url)

    if (!crawlResult.success) {
      throw new Error(crawlResult.message)
    }

    return {
      success: true,
      data: {
        ...crawlResult.data,
        magicType: 'web_crawl'
      },
      message: '魔法解析成功'
    }
  } catch (error) {
    console.error('魔法解析失败:', error)
    return {
      success: false,
      message: `魔法解析失败: ${error.message}`,
      error: error.stack
    }
  }
}

/**
 * 解析并提取 - 完整的魔法解析流程（优化版，解决超时问题）
 * @param {string} url 链接地址
 */
async function parseAndExtract(url) {
  try {
    console.log('开始完整魔法解析流程:', url)

    // 1. 爬取网页内容（设置3秒超时）
    const crawlResult = await Promise.race([
      crawlerService.magicParse(url),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('爬虫服务超时')), 3000)
      )
    ])

    if (!crawlResult.success) {
      throw new Error(crawlResult.message)
    }

    const { title, content, platform } = crawlResult.data

    // 2. 并行执行AI解析和地址提取（各设置3秒超时）
    const [aiResult, locationResult] = await Promise.allSettled([
      Promise.race([
        zhipuService.parseTravel(content),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('AI解析超时')), 3000)
        )
      ]),
      Promise.race([
        baiduService.extractAddresses(content),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('地址提取超时')), 3000)
        )
      ])
    ])

    // 3. 处理并行结果（容错处理）
    const aiData = aiResult.status === 'fulfilled' && aiResult.value.success
      ? aiResult.value.data
      : null

    const locationData = locationResult.status === 'fulfilled' && locationResult.value.success
      ? locationResult.value.data
      : []

    // 4. 整合结果
    const finalResult = {
      originalUrl: url,
      platform: platform,
      title: title,
      content: content,

      // AI解析结果
      aiParsed: aiData,

      // 地点提取结果
      locations: locationData,

      // 解析统计
      stats: {
        contentLength: content.length,
        locationCount: locationData.length,
        aiParseSuccess: aiResult.status === 'fulfilled' && aiResult.value.success,
        nlpParseSuccess: locationResult.status === 'fulfilled' && locationResult.value.success,
        processingTime: Date.now() - Date.now() // 实际处理时间会在调用时计算
      },

      parsedAt: new Date().toISOString()
    }

    return {
      success: true,
      data: finalResult,
      message: '完整解析成功'
    }
  } catch (error) {
    console.error('完整解析失败:', error)
    return {
      success: false,
      message: `完整解析失败: ${error.message}`,
      error: error.stack
    }
  }
}
