/**
 * 智能路线优化器
 * 集成遗传算法、模拟退火、蚁群算法的多算法融合优化系统
 * 支持用户偏好权重、实时约束处理和AI增强优化
 */

class RouteOptimizer {
  constructor(options = {}) {
    // 算法配置参数 - 正确的配置合并方式
    const defaultConfig = {
      genetic: {
        populationSize: 50,
        generations: 100,
        crossoverRate: 0.8,
        mutationRate: 0.1,
        eliteSize: 5,
        timeout: 300000 // 5分钟超时
      },
      simulated: {
        initialTemp: 1000,
        finalTemp: 1,
        coolingRate: 0.95,
        maxIterations: 1000,
        timeout: 300000
      },
      antColony: {
        antCount: 30,
        iterations: 100,
        alpha: 1.0, // 信息素重要程度
        beta: 2.0,  // 启发式信息重要程度
        rho: 0.1,   // 信息素挥发系数
        timeout: 300000
      }
    }

    // 正确合并配置：深度合并各个算法的配置
    this.config = {
      genetic: { ...defaultConfig.genetic, ...(options.genetic || {}) },
      simulated: { ...defaultConfig.simulated, ...(options.simulated || {}) },
      antColony: { ...defaultConfig.antColony, ...(options.antColony || {}) }
    }

    this.zhipuService = options.zhipuService
    this.distanceMatrix = null
    this.timeMatrix = null

    // 性能优化：添加计算缓存
    this.fitnessCache = new Map()
    this.distanceCache = new Map()
    this.bestSolutionHistory = []
  }

  /**
   * 主优化入口
   * @param {Array} locations 景点位置数组
   * @param {Object} preferences 用户偏好
   * @returns {Object} 优化结果
   */
  async optimize(locations, preferences = {}) {
    try {
      console.log('开始智能路线优化，景点数量:', locations.length)
      
      if (!locations || locations.length < 2) {
        throw new Error('景点数量不足，无法进行路线优化')
      }

      // 1. 预处理：计算距离和时间矩阵
      await this.preprocessData(locations)

      // 2. 并行执行多种算法
      const algorithmResults = await this.parallelOptimize(locations, preferences)

      // 3. 评估和选择最优解
      const bestSolution = this.selectBestSolution(algorithmResults, preferences)

      // 4. 应用实时约束
      const constrainedSolution = await this.applyConstraints(bestSolution, locations)

      // 5. AI增强优化
      const finalSolution = await this.aiEnhancement(constrainedSolution, locations, preferences)

      return {
        success: true,
        data: {
          optimizedRoute: finalSolution.route,
          totalDistance: finalSolution.totalDistance,
          totalTime: finalSolution.totalTime,
          optimizationScore: finalSolution.score,
          algorithmUsed: finalSolution.algorithm,
          aiEnhanced: finalSolution.aiEnhanced,
          optimizedAt: new Date().toISOString()
        }
      }

    } catch (error) {
      console.error('路线优化失败:', error)
      return {
        success: false,
        message: error.message,
        error: error.stack
      }
    }
  }

  /**
   * 预处理数据：计算距离和时间矩阵
   */
  async preprocessData(locations) {
    const n = locations.length
    this.distanceMatrix = Array(n).fill().map(() => Array(n).fill(0))
    this.timeMatrix = Array(n).fill().map(() => Array(n).fill(0))

    // 计算所有点对之间的距离和时间
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        if (i !== j) {
          const distance = this.calculateDistance(
            locations[i].coordinates.latitude,
            locations[i].coordinates.longitude,
            locations[j].coordinates.latitude,
            locations[j].coordinates.longitude
          )
          this.distanceMatrix[i][j] = distance
          // 简化时间计算：假设平均速度30km/h
          this.timeMatrix[i][j] = distance / 30 * 60 // 转换为分钟
        }
      }
    }
  }

  /**
   * 并行执行多种优化算法
   */
  async parallelOptimize(locations, preferences) {
    const algorithms = [
      this.runGeneticAlgorithm(locations, preferences),
      this.runSimulatedAnnealing(locations, preferences),
      this.runAntColonyOptimization(locations, preferences)
    ]

    try {
      const results = await Promise.allSettled(algorithms)
      
      return results.map((result, index) => {
        const algorithmNames = ['genetic', 'simulated', 'antColony']
        return {
          algorithm: algorithmNames[index],
          success: result.status === 'fulfilled',
          data: result.status === 'fulfilled' ? result.value : null,
          error: result.status === 'rejected' ? result.reason : null
        }
      })
    } catch (error) {
      console.error('并行算法执行失败:', error)
      throw error
    }
  }

  /**
   * 遗传算法实现 - 性能优化版本
   */
  async runGeneticAlgorithm(locations, preferences) {
    const startTime = Date.now()
    const config = this.config.genetic
    const n = locations.length

    // 性能优化：根据景点数量动态调整参数
    const adaptiveConfig = {
      ...config,
      populationSize: Math.min(config.populationSize, Math.max(10, n * 2)),
      generations: Math.min(config.generations, Math.max(20, n * 3))
    }

    // 初始化种群
    let population = this.initializePopulation(n, adaptiveConfig.populationSize)
    let bestSolution = null
    let bestFitness = Infinity
    let stagnationCount = 0 // 早停机制：连续无改善的代数
    const maxStagnation = Math.max(5, Math.floor(adaptiveConfig.generations * 0.2))

    for (let generation = 0; generation < adaptiveConfig.generations; generation++) {
      // 检查超时
      if (Date.now() - startTime > config.timeout) {
        console.log('遗传算法超时，提前结束')
        break
      }

      // 计算适应度（使用缓存优化）
      const fitnessScores = population.map(individual =>
        this.calculateFitnessWithCache(individual, locations, preferences)
      )

      // 更新最优解
      const currentBestIndex = fitnessScores.indexOf(Math.min(...fitnessScores))
      const currentBestFitness = fitnessScores[currentBestIndex]

      if (currentBestFitness < bestFitness) {
        bestFitness = currentBestFitness
        bestSolution = [...population[currentBestIndex]]
        stagnationCount = 0 // 重置停滞计数
      } else {
        stagnationCount++
      }

      // 早停机制：如果连续多代无改善，提前终止
      if (stagnationCount >= maxStagnation) {
        console.log(`遗传算法早停：连续${stagnationCount}代无改善`)
        break
      }

      // 选择、交叉、变异
      population = this.geneticEvolution(population, fitnessScores, adaptiveConfig)
    }

    return {
      route: bestSolution,
      fitness: bestFitness,
      totalDistance: this.calculateRouteDistance(bestSolution),
      totalTime: this.calculateRouteTime(bestSolution),
      generations: Math.min(adaptiveConfig.generations, Math.floor((Date.now() - startTime) / 100))
    }
  }

  /**
   * 模拟退火算法实现
   */
  async runSimulatedAnnealing(locations, preferences) {
    const startTime = Date.now()
    const config = this.config.simulated
    const n = locations.length
    
    // 初始解：随机排列
    let currentSolution = this.generateRandomRoute(n)
    let currentFitness = this.calculateFitness(currentSolution, locations, preferences)
    
    let bestSolution = [...currentSolution]
    let bestFitness = currentFitness
    
    let temperature = config.initialTemp
    let iteration = 0

    while (temperature > config.finalTemp && iteration < config.maxIterations) {
      // 检查超时
      if (Date.now() - startTime > config.timeout) {
        console.log('模拟退火算法超时，提前结束')
        break
      }

      // 生成邻域解
      const newSolution = this.generateNeighborSolution(currentSolution)
      const newFitness = this.calculateFitness(newSolution, locations, preferences)
      
      // 接受准则
      const delta = newFitness - currentFitness
      if (delta < 0 || Math.random() < Math.exp(-delta / temperature)) {
        currentSolution = newSolution
        currentFitness = newFitness
        
        // 更新最优解
        if (newFitness < bestFitness) {
          bestSolution = [...newSolution]
          bestFitness = newFitness
        }
      }
      
      // 降温
      temperature *= config.coolingRate
      iteration++
    }

    return {
      route: bestSolution,
      fitness: bestFitness,
      totalDistance: this.calculateRouteDistance(bestSolution),
      totalTime: this.calculateRouteTime(bestSolution),
      iterations: iteration,
      finalTemperature: temperature
    }
  }

  /**
   * 蚁群算法实现
   */
  async runAntColonyOptimization(locations, preferences) {
    const startTime = Date.now()
    const config = this.config.antColony
    const n = locations.length
    
    // 初始化信息素矩阵
    const pheromone = Array(n).fill().map(() => Array(n).fill(1.0))
    let bestSolution = null
    let bestFitness = Infinity

    for (let iteration = 0; iteration < config.iterations; iteration++) {
      // 检查超时
      if (Date.now() - startTime > config.timeout) {
        console.log('蚁群算法超时，提前结束')
        break
      }

      const antSolutions = []
      
      // 每只蚂蚁构建解
      for (let ant = 0; ant < config.antCount; ant++) {
        const solution = this.constructAntSolution(pheromone, locations, config)
        const fitness = this.calculateFitness(solution, locations, preferences)
        
        antSolutions.push({ route: solution, fitness: fitness })
        
        // 更新最优解
        if (fitness < bestFitness) {
          bestFitness = fitness
          bestSolution = [...solution]
        }
      }
      
      // 更新信息素
      this.updatePheromone(pheromone, antSolutions, config)
    }

    return {
      route: bestSolution,
      fitness: bestFitness,
      totalDistance: this.calculateRouteDistance(bestSolution),
      totalTime: this.calculateRouteTime(bestSolution),
      iterations: Math.min(config.iterations, Math.floor((Date.now() - startTime) / 1000))
    }
  }

  /**
   * 计算适应度函数
   */
  calculateFitness(route, locations, preferences) {
    const distanceScore = this.calculateRouteDistance(route)
    const timeScore = this.calculateRouteTime(route)
    const preferenceScore = this.calculatePreferenceScore(route, locations, preferences)
    
    // 权重配置
    const weights = {
      distance: preferences.preferShortest ? 0.5 : 0.3,
      time: preferences.preferFastest ? 0.4 : 0.3,
      preference: 0.4
    }
    
    // 归一化并加权
    return weights.distance * distanceScore + 
           weights.time * timeScore + 
           weights.preference * preferenceScore
  }

  /**
   * 计算路线总距离 - 添加安全检查
   */
  calculateRouteDistance(route) {
    if (!route || route.length < 2) return 0
    if (!this.distanceMatrix) return 0

    let totalDistance = 0
    for (let i = 0; i < route.length - 1; i++) {
      const from = route[i]
      const to = route[i + 1]

      // 安全检查：确保索引有效
      if (from >= 0 && from < this.distanceMatrix.length &&
          to >= 0 && to < this.distanceMatrix.length &&
          this.distanceMatrix[from] &&
          typeof this.distanceMatrix[from][to] === 'number') {
        totalDistance += this.distanceMatrix[from][to]
      }
    }
    return totalDistance
  }

  /**
   * 计算路线总时间 - 添加安全检查
   */
  calculateRouteTime(route) {
    if (!route || route.length < 2) return 0
    if (!this.timeMatrix) return 0

    let totalTime = 0
    for (let i = 0; i < route.length - 1; i++) {
      const from = route[i]
      const to = route[i + 1]

      // 安全检查：确保索引有效
      if (from >= 0 && from < this.timeMatrix.length &&
          to >= 0 && to < this.timeMatrix.length &&
          this.timeMatrix[from] &&
          typeof this.timeMatrix[from][to] === 'number') {
        totalTime += this.timeMatrix[from][to]
      }
    }
    return totalTime
  }

  /**
   * 计算两点间距离（公里）
   */
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  /**
   * 初始化种群
   */
  initializePopulation(n, populationSize) {
    const population = []
    for (let i = 0; i < populationSize; i++) {
      population.push(this.generateRandomRoute(n))
    }
    return population
  }

  /**
   * 生成随机路线
   */
  generateRandomRoute(n) {
    const route = Array.from({ length: n }, (_, i) => i)
    // Fisher-Yates 洗牌算法
    for (let i = route.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[route[i], route[j]] = [route[j], route[i]]
    }
    return route
  }

  /**
   * 遗传算法进化操作
   */
  geneticEvolution(population, fitnessScores, config) {
    const newPopulation = []

    // 精英保留
    const eliteIndices = this.selectElite(fitnessScores, config.eliteSize)
    eliteIndices.forEach(index => {
      newPopulation.push([...population[index]])
    })

    // 生成新个体
    while (newPopulation.length < config.populationSize) {
      // 选择父母
      const parent1 = this.tournamentSelection(population, fitnessScores)
      const parent2 = this.tournamentSelection(population, fitnessScores)

      // 交叉
      let offspring
      if (Math.random() < config.crossoverRate) {
        offspring = this.orderCrossover(parent1, parent2)
      } else {
        offspring = [...parent1]
      }

      // 变异
      if (Math.random() < config.mutationRate) {
        offspring = this.mutate(offspring)
      }

      newPopulation.push(offspring)
    }

    return newPopulation
  }

  /**
   * 锦标赛选择
   */
  tournamentSelection(population, fitnessScores, tournamentSize = 3) {
    let bestIndex = Math.floor(Math.random() * population.length)
    let bestFitness = fitnessScores[bestIndex]

    for (let i = 1; i < tournamentSize; i++) {
      const candidateIndex = Math.floor(Math.random() * population.length)
      if (fitnessScores[candidateIndex] < bestFitness) {
        bestIndex = candidateIndex
        bestFitness = fitnessScores[candidateIndex]
      }
    }

    return [...population[bestIndex]]
  }

  /**
   * 顺序交叉(OX) - 添加边界检查和防护
   */
  orderCrossover(parent1, parent2) {
    const n = parent1.length
    if (n <= 1) return [...parent1] // 边界检查

    const start = Math.floor(Math.random() * n)
    const maxEnd = Math.max(0, n - start)
    const end = Math.floor(Math.random() * maxEnd) + start

    // 确保end不超出范围
    const safeEnd = Math.min(end, n - 1)

    const offspring = Array(n).fill(-1)

    // 复制父母1的片段
    for (let i = start; i <= safeEnd; i++) {
      offspring[i] = parent1[i]
    }

    // 从父母2填充剩余位置
    let parent2Index = 0
    for (let i = 0; i < n; i++) {
      if (offspring[i] === -1) {
        // 防止无限循环：添加安全检查
        let attempts = 0
        while (offspring.includes(parent2[parent2Index]) && attempts < n) {
          parent2Index++
          if (parent2Index >= n) parent2Index = 0 // 循环回到开始
          attempts++
        }

        if (attempts < n) {
          offspring[i] = parent2[parent2Index]
          parent2Index++
        } else {
          // 如果找不到合适的元素，使用索引作为备用
          offspring[i] = i
        }
      }
    }

    return offspring
  }

  /**
   * 变异操作
   */
  mutate(individual) {
    const mutated = [...individual]
    const mutationType = Math.floor(Math.random() * 3)

    switch (mutationType) {
      case 0: // 交换变异
        return this.swapMutation(mutated)
      case 1: // 插入变异
        return this.insertMutation(mutated)
      case 2: // 逆序变异
        return this.reverseMutation(mutated)
      default:
        return mutated
    }
  }

  /**
   * 交换变异
   */
  swapMutation(individual) {
    const n = individual.length
    const i = Math.floor(Math.random() * n)
    const j = Math.floor(Math.random() * n)
    ;[individual[i], individual[j]] = [individual[j], individual[i]]
    return individual
  }

  /**
   * 插入变异
   */
  insertMutation(individual) {
    const n = individual.length
    const i = Math.floor(Math.random() * n)
    const j = Math.floor(Math.random() * n)

    const element = individual.splice(i, 1)[0]
    individual.splice(j, 0, element)
    return individual
  }

  /**
   * 逆序变异
   */
  reverseMutation(individual) {
    const n = individual.length
    const start = Math.floor(Math.random() * n)
    const end = Math.floor(Math.random() * (n - start)) + start

    const reversed = individual.slice(start, end + 1).reverse()
    return [...individual.slice(0, start), ...reversed, ...individual.slice(end + 1)]
  }

  /**
   * 选择精英个体
   */
  selectElite(fitnessScores, eliteSize) {
    return fitnessScores
      .map((fitness, index) => ({ fitness, index }))
      .sort((a, b) => a.fitness - b.fitness)
      .slice(0, eliteSize)
      .map(item => item.index)
  }

  /**
   * 生成邻域解（用于模拟退火）
   */
  generateNeighborSolution(solution) {
    const neighbor = [...solution]
    const operationType = Math.floor(Math.random() * 3)

    switch (operationType) {
      case 0: // 2-opt
        return this.twoOptMove(neighbor)
      case 1: // 3-opt
        return this.threeOptMove(neighbor)
      case 2: // 插入操作
        return this.insertMove(neighbor)
      default:
        return this.swapMutation(neighbor)
    }
  }

  /**
   * 2-opt邻域操作 - 添加边界检查
   */
  twoOptMove(solution) {
    const n = solution.length
    if (n < 2) return [...solution] // 边界检查：至少需要2个点

    const maxI = Math.max(0, n - 1)
    if (maxI <= 0) return [...solution]

    const i = Math.floor(Math.random() * maxI)

    const maxJ = Math.max(0, n - i - 1)
    if (maxJ <= 0) return [...solution]

    const j = Math.floor(Math.random() * maxJ) + i + 1

    // 边界检查：确保j不超出范围
    if (j >= n) return [...solution]

    // 逆转i到j之间的路径
    const newSolution = [...solution]
    const reversed = newSolution.slice(i, j + 1).reverse()
    return [...newSolution.slice(0, i), ...reversed, ...newSolution.slice(j + 1)]
  }

  /**
   * 3-opt邻域操作（简化版）- 修复边界检查问题
   */
  threeOptMove(solution) {
    const n = solution.length
    if (n < 6) return this.twoOptMove(solution) // 太短则使用2-opt

    // 修复：确保随机数生成不会产生负数或无效范围
    const maxI = Math.max(0, n - 5)
    if (maxI <= 0) return this.twoOptMove(solution)

    const i = Math.floor(Math.random() * maxI)

    const maxJ = Math.max(0, n - i - 3)
    if (maxJ <= 0) return this.twoOptMove(solution)

    const j = Math.floor(Math.random() * maxJ) + i + 2

    const maxK = Math.max(0, n - j - 1)
    if (maxK <= 0) return this.twoOptMove(solution)

    const k = Math.floor(Math.random() * maxK) + j + 1

    // 边界检查：确保索引有效
    if (i >= j || j >= k || k >= n) {
      return this.twoOptMove(solution)
    }

    // 简化的3-opt：重新连接三个片段
    const segment1 = solution.slice(0, i)
    const segment2 = solution.slice(i, j)
    const segment3 = solution.slice(j, k)
    const segment4 = solution.slice(k)

    // 随机选择一种重连方式
    const reconnectType = Math.floor(Math.random() * 3)
    switch (reconnectType) {
      case 0:
        return [...segment1, ...segment3.reverse(), ...segment2, ...segment4]
      case 1:
        return [...segment1, ...segment2.reverse(), ...segment3.reverse(), ...segment4]
      case 2:
        return [...segment1, ...segment3, ...segment2.reverse(), ...segment4]
      default:
        return solution
    }
  }

  /**
   * 插入邻域操作
   */
  insertMove(solution) {
    const n = solution.length
    const i = Math.floor(Math.random() * n)
    const j = Math.floor(Math.random() * n)

    const newSolution = [...solution]
    const element = newSolution.splice(i, 1)[0]
    newSolution.splice(j, 0, element)
    return newSolution
  }

  /**
   * 蚁群算法构建解
   */
  constructAntSolution(pheromone, locations, config) {
    const n = locations.length
    const visited = Array(n).fill(false)
    const solution = []

    // 随机选择起始点
    let current = Math.floor(Math.random() * n)
    solution.push(current)
    visited[current] = true

    // 构建完整路径
    for (let step = 1; step < n; step++) {
      const next = this.selectNextCity(current, visited, pheromone, config)
      solution.push(next)
      visited[next] = true
      current = next
    }

    return solution
  }

  /**
   * 蚁群算法选择下一个城市 - 添加安全检查
   */
  selectNextCity(current, visited, pheromone, config) {
    const n = visited.length
    const probabilities = []
    let totalProb = 0

    // 边界检查
    if (current < 0 || current >= n) {
      console.error('当前城市索引无效:', current)
      return 0
    }

    // 计算转移概率
    for (let i = 0; i < n; i++) {
      if (!visited[i]) {
        // 安全检查：确保矩阵索引有效
        if (this.distanceMatrix && this.distanceMatrix[current] &&
            typeof this.distanceMatrix[current][i] === 'number') {
          const distance = Math.max(0.1, this.distanceMatrix[current][i]) // 防止除零
          const pheromoneLevel = Math.pow(Math.max(0.01, pheromone[current][i]), config.alpha)
          const heuristic = Math.pow(1.0 / distance, config.beta)
          const prob = pheromoneLevel * heuristic
          probabilities[i] = prob
          totalProb += prob
        } else {
          probabilities[i] = 0.01 // 默认小概率
          totalProb += 0.01
        }
      } else {
        probabilities[i] = 0
      }
    }

    // 防止totalProb为0
    if (totalProb <= 0) {
      const unvisited = []
      for (let i = 0; i < n; i++) {
        if (!visited[i]) unvisited.push(i)
      }
      return unvisited.length > 0 ? unvisited[Math.floor(Math.random() * unvisited.length)] : 0
    }

    // 轮盘赌选择
    const random = Math.random() * totalProb
    let cumulative = 0

    for (let i = 0; i < n; i++) {
      if (!visited[i]) {
        cumulative += probabilities[i]
        if (random <= cumulative) {
          return i
        }
      }
    }

    // 备用选择：随机选择未访问的城市
    const unvisited = []
    for (let i = 0; i < n; i++) {
      if (!visited[i]) unvisited.push(i)
    }
    return unvisited.length > 0 ? unvisited[Math.floor(Math.random() * unvisited.length)] : 0
  }

  /**
   * 更新信息素
   */
  updatePheromone(pheromone, antSolutions, config) {
    const n = pheromone.length

    // 信息素挥发
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        pheromone[i][j] *= (1 - config.rho)
      }
    }

    // 信息素增强
    antSolutions.forEach(solution => {
      const route = solution.route
      const fitness = solution.fitness
      const pheromoneDeposit = 1.0 / (fitness + 1) // 适应度越好，信息素越多

      for (let i = 0; i < route.length - 1; i++) {
        const from = route[i]
        const to = route[i + 1]
        pheromone[from][to] += pheromoneDeposit
        pheromone[to][from] += pheromoneDeposit // 对称矩阵
      }
    })
  }

  /**
   * 计算偏好得分
   */
  calculatePreferenceScore(route, locations, preferences) {
    let score = 0
    const n = route.length

    // 景点类型偏好
    if (preferences.preferredTypes && preferences.preferredTypes.length > 0) {
      for (let i = 0; i < n; i++) {
        const location = locations[route[i]]
        if (preferences.preferredTypes.includes(location.type)) {
          score += 10 // 偏好景点加分
        }
      }
    }

    // 预算偏好
    if (preferences.budgetConscious) {
      let totalCost = 0
      for (let i = 0; i < n; i++) {
        const location = locations[route[i]]
        totalCost += location.ticketPrice || 0
      }
      score += preferences.budgetConscious * (1000 - totalCost) / 1000 * 20
    }

    // 体力偏好（距离相关）
    if (preferences.energyLevel) {
      const totalDistance = this.calculateRouteDistance(route)
      const energyPenalty = (5 - preferences.energyLevel) * totalDistance * 0.1
      score -= energyPenalty
    }

    return Math.max(0, score) // 确保得分非负
  }

  /**
   * 选择最优解
   */
  selectBestSolution(algorithmResults, preferences) {
    let bestSolution = null
    let bestScore = Infinity
    let bestAlgorithm = null

    algorithmResults.forEach(result => {
      if (result.success && result.data) {
        // 综合评分：距离 + 时间 + 算法特定得分
        const normalizedDistance = result.data.totalDistance / 100 // 归一化距离
        const normalizedTime = result.data.totalTime / 60 // 归一化时间
        const algorithmScore = result.data.fitness || 0

        const weights = {
          distance: preferences.preferShortest ? 0.4 : 0.3,
          time: preferences.preferFastest ? 0.4 : 0.3,
          algorithm: 0.3
        }

        const totalScore = weights.distance * normalizedDistance +
                          weights.time * normalizedTime +
                          weights.algorithm * algorithmScore

        if (totalScore < bestScore) {
          bestScore = totalScore
          bestSolution = result.data
          bestAlgorithm = result.algorithm
        }
      }
    })

    if (!bestSolution) {
      throw new Error('所有算法都执行失败')
    }

    return {
      ...bestSolution,
      algorithm: bestAlgorithm,
      score: bestScore
    }
  }

  /**
   * 应用实时约束
   */
  async applyConstraints(solution, locations) {
    try {
      const constrainedRoute = [...solution.route]
      let modified = false

      // 检查营业时间约束
      for (let i = 0; i < constrainedRoute.length; i++) {
        const locationIndex = constrainedRoute[i]
        const location = locations[locationIndex]

        if (location.openTime && location.closeTime) {
          // 简化的时间检查逻辑
          const currentHour = new Date().getHours()
          const openHour = parseInt(location.openTime.split(':')[0])
          const closeHour = parseInt(location.closeTime.split(':')[0])

          if (currentHour < openHour || currentHour > closeHour) {
            // 如果当前时间不在营业时间内，可以考虑调整顺序
            // 这里简化处理，实际应用中需要更复杂的时间规划
            console.log(`景点 ${location.name} 当前不在营业时间内`)
          }
        }
      }

      // 重新计算距离和时间
      const totalDistance = this.calculateRouteDistance(constrainedRoute)
      const totalTime = this.calculateRouteTime(constrainedRoute)

      return {
        ...solution,
        route: constrainedRoute,
        totalDistance: totalDistance,
        totalTime: totalTime,
        constraintsApplied: true,
        modified: modified
      }

    } catch (error) {
      console.error('应用约束失败:', error)
      return solution // 返回原解决方案
    }
  }

  /**
   * AI增强优化
   */
  async aiEnhancement(solution, locations, preferences) {
    try {
      if (!this.zhipuService) {
        console.log('未配置智谱AI服务，跳过AI增强')
        return { ...solution, aiEnhanced: false }
      }

      // 构建AI优化提示
      const routeDescription = solution.route.map(index => locations[index].name).join(' -> ')
      const prompt = `
请分析以下旅游路线的合理性并提供优化建议：

路线：${routeDescription}
总距离：${solution.totalDistance.toFixed(2)}公里
总时间：${solution.totalTime.toFixed(0)}分钟
用户偏好：${JSON.stringify(preferences)}

请从以下角度分析：
1. 路线的地理合理性
2. 时间安排的合理性
3. 景点搭配的合理性
4. 是否有更好的游览顺序建议

请返回JSON格式的分析结果：
{
  "reasonableScore": 0-10,
  "suggestions": ["建议1", "建议2"],
  "optimizedOrder": [景点索引数组],
  "reasoning": "优化理由"
}
`

      const aiResult = await this.zhipuService.callAPI('/chat/completions', {
        model: 'glm-4-flash',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 1000
      })

      const aiContent = aiResult.choices[0].message.content

      try {
        const aiAnalysis = JSON.parse(aiContent)

        // 如果AI建议的路线更好，则采用
        if (aiAnalysis.reasonableScore < 7 && aiAnalysis.optimizedOrder) {
          const aiRoute = aiAnalysis.optimizedOrder
          if (this.isValidRoute(aiRoute, locations.length)) {
            const aiDistance = this.calculateRouteDistance(aiRoute)
            const aiTime = this.calculateRouteTime(aiRoute)

            return {
              route: aiRoute,
              totalDistance: aiDistance,
              totalTime: aiTime,
              score: solution.score * 0.9, // AI优化给予10%的奖励
              algorithm: solution.algorithm + '+AI',
              aiEnhanced: true,
              aiAnalysis: aiAnalysis
            }
          }
        }

        return {
          ...solution,
          aiEnhanced: true,
          aiAnalysis: aiAnalysis
        }

      } catch (parseError) {
        console.error('AI响应解析失败:', parseError)
        return {
          ...solution,
          aiEnhanced: false,
          aiError: parseError.message
        }
      }

    } catch (error) {
      console.error('AI增强失败:', error)
      return {
        ...solution,
        aiEnhanced: false,
        aiError: error.message
      }
    }
  }

  /**
   * 验证路线有效性
   */
  isValidRoute(route, locationCount) {
    if (!Array.isArray(route) || route.length !== locationCount) {
      return false
    }

    const visited = new Set()
    for (const index of route) {
      if (typeof index !== 'number' || index < 0 || index >= locationCount || visited.has(index)) {
        return false
      }
      visited.add(index)
    }

    return true
  }
}

module.exports = RouteOptimizer
