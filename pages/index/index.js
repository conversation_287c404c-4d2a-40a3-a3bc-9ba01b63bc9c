// pages/index/index.js
import storage from '../../utils/storage.js'
import simpleDataManager from '../../utils/simple-data-manager.js'
import auth from '../../utils/auth.js'
import userManager from '../../utils/userManager.js'
import { PAGES } from '../../utils/constants.js'
import Toast from '../../miniprogram_npm/@vant/weapp/toast/toast'
import unifiedPerformanceManager from '../../utils/unified-performance-manager.js'
import hapticManager from '../../utils/haptic-manager.js'
// {{ AURA-X: Add - 添加缺失的dataChangeNotifier导入. Approval: 寸止(ID:1738056000). }}
import dataChangeNotifier from '../../utils/data-change-notifier.js'


Page({
  data: {
    appName: '爱巢小记',
    motto: '让记账变得简单而美好',
    userInfo: {},
    hasUserInfo: false,
    hasNotification: true, // 添加通知状态
    isGuest: false,
    // 财务概览数据 - 移除硬编码，使用真实数据
    financialOverview: {
      totalExpense: 0,           // 本月总支出
      dailyExpense: 0,           // 日常支出
      travelExpense: 0,          // 旅行支出
      totalBudget: 0,            // 总预算
      remainingBudget: 0,        // 剩余预算
      usagePercentage: 0,        // 使用百分比
      dailyPercentage: 0,        // 日常支出占比
      travelPercentage: 0        // 旅行支出占比
    },
    // 数据加载状态
    loading: {
      financial: false
    },
    // 页面状态管理
    _lastLoadTime: 0,           // 上次数据加载时间
    _needsRefresh: false,       // 是否需要刷新数据

    // {{ AURA-X: Add - 旅行数据状态. Approval: 寸止(ID:1738056000). }}
    // 旅行数据
    travelData: {
      currentPlan: null,        // 当前计划
      stats: {                  // 统计数据
        totalPlans: 0,
        activePlans: 0,
        completedPlans: 0,
        totalExpenses: 0,
        totalBudget: 0
      },
      recentPlans: [],          // 最近计划
      lastUpdate: 0             // 最后更新时间
    },
    // 旅行数据加载状态
    travelLoading: false
  },

  async onLoad() {
    // 初始化简化数据管理器
    await simpleDataManager.initialize()

    // 登录检查
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/index'
      })
      return
    }

    // 使用用户管理器初始化页面
    await userManager.mixinPage(this)

    // 注册数据变更监听器
    this.setupDataChangeListeners()

    // 加载财务数据（使用优化版本）
    this.loadFinancialDataOptimized()
  },



  async onShow() {
    // 🔥 检查登录状态
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/index'
      })
      return
    }

    // 页面可见时刷新数据
    console.log('首页显示，检查数据更新')

    try {
      // 智能刷新逻辑：只在必要时刷新数据
      const now = Date.now()
      const timeSinceLastLoad = now - this.data._lastLoadTime
      const shouldRefresh = this.data._needsRefresh ||
                           timeSinceLastLoad > 5 * 60 * 1000 || // 5分钟后自动刷新
                           this.data._lastLoadTime === 0        // 首次加载

      // 并行处理用户信息和财务数据
      const loadPromises = []

      // 用户信息处理（减少不必要的刷新）
      if (!this.data.userInfo || !this.data.userInfo.openid || shouldRefresh) {
        loadPromises.push(
          userManager.forceRefreshUserInfo()
            .then(() => userManager.mixinPage(this))
            .then(() => this.refreshUserDisplay())
        )
      }

      // 财务数据处理（使用简化数据管理器）
      if (shouldRefresh) {
        loadPromises.push(this.loadFinancialDataOptimized())
      }

      // 旅行数据处理（使用简化数据管理器）
      if (shouldRefresh) {
        loadPromises.push(this.loadTravelDataOptimized())
      }

      // 并行执行所有加载任务
      if (loadPromises.length > 0) {
        await Promise.allSettled(loadPromises)

        if (shouldRefresh) {
          unifiedPerformanceManager.smartSetData(this, {
            _lastLoadTime: now,
            _needsRefresh: false
          })
        }
      }

    } catch (error) {
      // 如果用户信息验证失败，跳转到登录页
      wx.reLaunch({
        url: '/pages/login/index'
      })
    }
  },

  // {{ AURA-X: Modify - 移除不存在的realtimeSyncManager调用. Approval: 寸止(ID:1738056000). }}
  onHide() {
    // 页面隐藏时清理资源
    // 注：移除了不存在的realtimeSyncManager调用，依赖现有同步机制
  },

  // 刷新用户信息显示
  refreshUserDisplay() {
    const userInfo = userManager.getUserInfo()

    // 使用智能setData，只更新变化的数据
    unifiedPerformanceManager.smartSetData(this, {
      userInfo: userInfo,
      hasUserInfo: !!userInfo && !userInfo.isGuest,
      isGuest: userInfo.isGuest || false
    })
  },

  onUnload() {
    // 清理用户管理器监听器
    userManager.cleanupPage(this)

    // 清理性能管理器资源
    unifiedPerformanceManager.cleanup()

    // 清理数据变更监听器
    dataChangeNotifier.cleanupPage(this)

    // 清理简化数据管理器监听器
    // 新系统无需手动清理，自动管理
  },

  // 设置数据变更监听器（简化版）
  setupDataChangeListeners() {
    // 监听财务数据变更
    simpleDataManager.onChange('financial_overview', (data) => {
      if (data) {
        const processedData = this.processFinancialData(data)
        unifiedPerformanceManager.smartSetData(this, {
          financialOverview: processedData
        })
      }
    })

    // 监听旅行数据变更
    simpleDataManager.onChange('travel_overview', (data) => {
      if (data) {
        const processedData = {
          currentPlan: data.currentPlan || null,
          stats: {
            totalPlans: data.totalPlans || 0,
            activePlans: data.ongoingPlans || 0,
            completedPlans: data.completedPlans || 0,
            totalExpense: data.totalExpense || 0
          }
        }
        unifiedPerformanceManager.smartSetData(this, {
          travelData: processedData
        })
      }
    })
  },

  // 手动刷新数据（优化版）
  onRefresh() {
    unifiedPerformanceManager.smartSetData(this, { _needsRefresh: true })

    // 清理缓存并重新加载
    simpleDataManager.clearCache()
    this.loadFinancialDataOptimized(true)
    this.loadTravelDataOptimized(true)

    // 触觉反馈
    hapticManager.impact('light')
  },

  // 处理财务数据，修复百分比显示问题
  processFinancialData(data) {
    const processedData = { ...data }

    // 修复百分比显示问题
    if (processedData.usagePercentage) {
      processedData.usagePercentage = Math.round(processedData.usagePercentage * 100) / 100
    }

    if (processedData.dailyPercentage) {
      processedData.dailyPercentage = Math.round(processedData.dailyPercentage * 100) / 100
    }

    if (processedData.travelPercentage) {
      processedData.travelPercentage = Math.round(processedData.travelPercentage * 100) / 100
    }

    // 计算剩余预算百分比
    if (processedData.totalBudget > 0) {
      const remainingPercentage = 100 - (processedData.usagePercentage || 0)
      processedData.remainingPercentage = Math.max(0, Math.round(remainingPercentage * 100) / 100)
    } else {
      processedData.remainingPercentage = 0
    }

    return processedData
  },

  // 加载财务数据
  async loadFinancialData(forceRefresh = false) {
    // 再次检查登录状态，确保安全
    if (!auth.isLoggedIn()) {
      return
    }

    // 设置加载状态
    unifiedPerformanceManager.smartSetData(this, {
      'loading.financial': true
    })

    try {
      // 使用简化数据管理器获取财务数据
      const result = await simpleDataManager.getData(
        'financial_overview',
        async () => {
          // 云函数调用作为备选
          return await wx.cloud.callFunction({
            name: 'expense',
            data: { action: 'getFinancialOverview' }
          }).then(res => res.result)
        },
        { forceRefresh }
      )

      if (result.success && result.data) {
        // 处理财务数据
        const processedData = this.processFinancialData(result.data)

        // 更新财务数据
        unifiedPerformanceManager.smartSetData(this, {
          financialOverview: processedData
        })
      } else {
        // 获取失败，使用默认数据
        this.setDefaultFinancialData()
      }
    } catch (error) {
      this.setDefaultFinancialData()
    } finally {
      // 清除加载状态
      unifiedPerformanceManager.smartSetData(this, {
        'loading.financial': false
      })
    }
  },

  // 优化的财务数据加载（使用简化数据管理器）
  async loadFinancialDataOptimized(forceRefresh = false) {
    if (!auth.isLoggedIn()) return

    // 设置加载状态
    unifiedPerformanceManager.smartSetData(this, {
      'loading.financial': true
    })

    try {
      // 使用简化数据管理器，本地优先
      const result = await simpleDataManager.getData(
        'financial_overview',
        async () => {
          // 云函数调用作为备选
          return await wx.cloud.callFunction({
            name: 'expense',
            data: { action: 'getFinancialOverview' }
          }).then(res => res.result)
        },
        { forceRefresh }
      )

      if (result.success && result.data) {
        // 本地计算处理数据（避免云函数调用）
        const processedData = simpleDataManager.calculateLocally(
          'processed_financial',
          () => this.processFinancialData(result.data),
          [JSON.stringify(result.data)]
        )

        // 立即更新UI
        unifiedPerformanceManager.smartSetData(this, {
          financialOverview: processedData
        })
      } else {
        this.setDefaultFinancialData()
      }
    } catch (error) {
      console.error('财务数据加载失败:', error)
      this.setDefaultFinancialData()
    } finally {
      unifiedPerformanceManager.smartSetData(this, {
        'loading.financial': false
      })
    }
  },

  // 优化的旅行数据加载
  async loadTravelDataOptimized(forceRefresh = false) {
    try {
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: true
      })

      // 使用简化数据管理器，本地优先
      const result = await simpleDataManager.getData(
        'travel_overview',
        async () => {
          // 云函数调用作为备选
          return await wx.cloud.callFunction({
            name: 'travel',
            data: { action: 'getTravelStatistics' }
          }).then(res => res.result)
        },
        { forceRefresh }
      )

      if (result.success && result.data) {
        // 本地计算处理数据
        const processedData = simpleDataManager.calculateLocally(
          'processed_travel',
          () => ({
            currentPlan: result.data.currentPlan || null,
            stats: {
              totalPlans: result.data.totalPlans || 0,
              activePlans: result.data.ongoingPlans || 0,
              completedPlans: result.data.completedPlans || 0,
              totalExpense: result.data.totalExpense || 0
            }
          }),
          [JSON.stringify(result.data)]
        )

        unifiedPerformanceManager.smartSetData(this, {
          travelData: processedData
        })
      }
    } catch (error) {
      console.error('旅行数据加载失败:', error)
    } finally {
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: false
      })
    }
  },

  // {{ AURA-X: Modify - 使用统一数据服务优化旅行数据加载. Approval: 寸止(ID:1738056000). }}
  /**
   * 加载旅行数据 - 本地优先策略
   * @param {boolean} forceRefresh 强制刷新
   */
  async loadTravelData(forceRefresh = false) {
    try {
      // 设置加载状态
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: true
      })

      // 使用简化数据管理器获取旅行数据
      const result = await simpleDataManager.getData(
        'travel_overview',
        async () => {
          // 云函数调用作为备选
          return await wx.cloud.callFunction({
            name: 'travel',
            data: { action: 'getTravelStatistics' }
          }).then(res => res.result)
        },
        { forceRefresh }
      )

      // 处理数据
      const stats = result.success && result.data ? result.data : this.getDefaultStats()
      const plans = result.success && result.data && result.data.recentPlans ? result.data.recentPlans : []

      // 获取当前计划
      const currentPlan = plans.find(plan => {
        const today = new Date()
        const startDate = new Date(plan.startDate)
        const endDate = new Date(plan.endDate)
        return today >= startDate && today <= endDate
      }) || null

      // 组装旅行数据
      const travelData = {
        stats,
        recentPlans: plans.slice(0, 3),
        currentPlan,
        lastUpdate: Date.now()
      }

      // 更新旅行数据
      unifiedPerformanceManager.smartSetData(this, {
        travelData
      })

      // 页面数据已更新

    } catch (error) {
      console.error('加载旅行数据失败:', error)
      this.setDefaultTravelData()
    } finally {
      // 清除加载状态
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: false
      })
    }
  },

  /**
   * 设置旅行数据实时同步（简化版）
   */
  setupTravelDataSync() {
    // 使用简化数据管理器的变更监听
    simpleDataManager.onChange('travel_overview', (data) => {
      if (data) {
        this.onTravelDataUpdate(data)
      }
    })
  },



  /**
   * 旅行数据更新回调
   * @param {Object} data 旅行数据
   */
  onTravelDataUpdate(data) {
    const processedData = {
      currentPlan: data.currentPlan || null,
      stats: {
        totalPlans: data.totalPlans || 0,
        activePlans: data.ongoingPlans || 0,
        completedPlans: data.completedPlans || 0,
        totalExpense: data.totalExpense || 0
      },
      recentPlans: data.recentPlans ? data.recentPlans.slice(0, 3) : [],
      lastUpdate: Date.now()
    }

    unifiedPerformanceManager.smartSetData(this, {
      travelData: processedData
    })
  },

  /**
   * 设置默认旅行数据
   */
  setDefaultTravelData() {
    unifiedPerformanceManager.smartSetData(this, {
      travelData: {
        currentPlan: null,
        stats: {
          totalPlans: 0,
          completedPlans: 0,
          ongoingPlans: 0,
          plannedPlans: 0,
          totalExpense: 0,
          thisMonthExpense: 0,
          avgExpensePerTrip: 0,
          popularDestinations: []
        },
        recentPlans: [],
        lastUpdate: Date.now()
      }
    })
  },

  /**
   * 查看当前旅行计划
   */
  viewCurrentPlan() {
    const currentPlan = this.data.travelData.currentPlan
    if (!currentPlan) {
      Toast.fail('暂无进行中的旅行计划')
      return
    }

    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${currentPlan._id || currentPlan.id}`
    })
  },

  /**
   * 查看旅行计划详情
   * @param {Event} e 事件对象
   */
  viewTravelPlan(e) {
    const planId = e.currentTarget.dataset.planId
    if (!planId) return

    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${planId}`
    })
  },

  /**
   * 前往旅行规划页面
   */
  goToTravelPlanning() {
    wx.navigateTo({
      url: '/subpackages/travel-planning/index'
    })
  },

  /**
   * 刷新旅行数据
   */
  async refreshTravelData() {
    await this.loadTravelDataOptimized(true)
  },

  // 设置默认财务数据
  setDefaultFinancialData() {
    const defaultData = {
      totalExpense: 0,
      dailyExpense: 0,
      travelExpense: 0,
      budgetUsage: 0,
      remainingBudget: 0,
      totalBudget: 0,
      usagePercentage: 0,
      dailyPercentage: 0,
      travelPercentage: 0
    }

    unifiedPerformanceManager.smartSetData(this, {
      financialOverview: defaultData
    })
  },



  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        unifiedPerformanceManager.smartSetData(this, {
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },

  getUserInfo(e) {
    // 不推荐使用getUserInfo获取用户信息，预计自2021年4月13日起，getUserInfo将不再弹出弹窗，并直接返回匿名的用户个人信息
    unifiedPerformanceManager.smartSetData(this, {
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },



  // 导航到旅行规划
  navigateToTravelPlanning() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/index'
    })
  },

  // 导航到记账管理 - 默认日常记账
  navigateToAccounting() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/account/travel-expense/index?mode=daily'
    })
  },

  // 导航到社交功能
  navigateToSocial() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/social/discover/index'
    })
  },

  // 导航到发现页面
  navigateToDiscover() {
    hapticManager.navigation()
    wx.switchTab({
      url: '/subpackages/social/discover/index'
    })
  },

  // 导航到预算设置
  navigateToBudgetSetting() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/settings/budget-setting/index'
    })
  },

  // 导航到个人中心
  navigateToProfile() {
    hapticManager.navigation()
    wx.navigateTo({
      url: PAGES.PROFILE
    })
  }
})
