/* pages/performance-monitor/index.wxss */
.performance-monitor {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 通用区块样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 性能指标 */
.metrics-section {
  margin-bottom: 40rpx;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.metric-card {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.metric-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.metric-value.excellent { color: #52c41a; }
.metric-value.good { color: #1890ff; }
.metric-value.fair { color: #faad14; }
.metric-value.poor { color: #f5222d; }

.metric-label {
  font-size: 24rpx;
  color: #666;
}

/* 系统状态 */
.status-section {
  margin-bottom: 40rpx;
}

.status-card {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.status-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #d9d9d9;
}

.status-indicator.active {
  background: #52c41a;
  animation: pulse 2s infinite;
}

.status-indicator.idle {
  background: #d9d9d9;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.status-details text {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮 */
.actions-section {
  margin-bottom: 40rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.action-btn {
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  background: white;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  grid-column: 1 / -1;
}

.action-btn.warning {
  background: #faad14;
  color: white;
}

.action-btn:disabled {
  opacity: 0.6;
}

/* 实时日志 */
.logs-section {
  margin-bottom: 40rpx;
}

.log-actions {
  display: flex;
  gap: 10rpx;
}

.mini-btn {
  padding: 10rpx 20rpx;
  font-size: 22rpx;
  border-radius: 8rpx;
  border: 1rpx solid #d9d9d9;
  background: white;
  color: #666;
}

.logs-container {
  height: 400rpx;
  background: #1a1a1a;
  border-radius: 12rpx;
  padding: 20rpx;
}

.log-item {
  font-size: 22rpx;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  line-height: 1.5;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.empty-logs {
  text-align: center;
  color: #666;
  font-size: 24rpx;
  padding: 60rpx 0;
}

/* 性能提示 */
.tips-section {
  margin-bottom: 40rpx;
}

.tips-list {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .performance-monitor {
    background: #1a1a1a;
  }
  
  .metric-card,
  .status-card,
  .tips-list {
    background: #2a2a2a;
    color: #fff;
  }
  
  .section-title {
    color: #fff;
  }
  
  .status-details text {
    color: #ccc;
  }
  
  .tip-text {
    color: #ccc;
  }
}
