// subpackages/travel-planning/expense-list/index.js
// {{ AURA-X: Modify - 替换不存在的data-manager为simpleDataManager. Approval: 寸止(ID:1738056000). }}
const simpleDataManager = require('../../../utils/simple-data-manager.js').default
const unifiedPerformanceManager = require('../../../utils/unified-performance-manager.js').default

Page({
  data: {
    planId: '',
    planTitle: '',
    expenseRecords: [],
    loading: false,
    totalAmount: 0,
    recordCount: 0,
    isEmpty: false
  },

  onLoad(options) {
    if (options.planId) {
      this.setData({
        planId: options.planId,
        planTitle: options.planTitle || '旅行费用记录'
      })
      this.loadExpenseRecords()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 加载费用记录
   */
  async loadExpenseRecords() {
    try {
      this.setData({ loading: true })

      // {{ AURA-X: Modify - 使用本地缓存替代dataManager调用. Approval: 寸止(ID:1738056000). }}
      // 从本地缓存获取费用记录数据
      const allExpenses = simpleDataManager.getFromLocalStorage('expense_records_cache') || []

      // 筛选与当前旅行计划相关的费用记录
      const planExpenses = allExpenses
        .filter(expense => expense.travel_plan_id === this.data.planId || expense.planId === this.data.planId)
        .sort((a, b) => new Date(b.createTime || b.date) - new Date(a.createTime || a.date))
        .slice(0, 100) // 加载更多记录

      const result = {
        success: true,
        data: planExpenses
      }

      if (result.success && result.data) {
        // 格式化数据
        const formattedRecords = result.data.map(record => ({
          id: record._id,
          amount: record.amount,
          category: record.category?.main || '其他',
          description: record.description || record.category?.main || '支出',
          date: this.formatDate(record.createTime),
          fullDate: this.formatFullDate(record.createTime),
          location: record.location?.name || '',
          createTime: record.createTime
        }))

        // 计算总金额
        const totalAmount = formattedRecords.reduce((sum, record) => sum + record.amount, 0)

        this.setData({
          expenseRecords: formattedRecords,
          totalAmount: totalAmount,
          recordCount: formattedRecords.length,
          isEmpty: formattedRecords.length === 0
        })
      } else {
        this.setData({
          expenseRecords: [],
          totalAmount: 0,
          recordCount: 0,
          isEmpty: true
        })
      }
    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({
        expenseRecords: [],
        isEmpty: true
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 格式化日期显示 (MM-DD)
   */
  formatDate(dateInput) {
    try {
      let date
      if (dateInput instanceof Date) {
        date = dateInput
      } else if (typeof dateInput === 'string') {
        date = new Date(dateInput)
      } else if (dateInput && dateInput.$date) {
        date = new Date(dateInput.$date)
      } else {
        date = new Date()
      }

      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${month}-${day}`
    } catch (error) {
      return '今天'
    }
  },

  /**
   * 格式化完整日期显示 (YYYY-MM-DD HH:mm)
   */
  formatFullDate(dateInput) {
    try {
      let date
      if (dateInput instanceof Date) {
        date = dateInput
      } else if (typeof dateInput === 'string') {
        date = new Date(dateInput)
      } else if (dateInput && dateInput.$date) {
        date = new Date(dateInput.$date)
      } else {
        date = new Date()
      }

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      return '未知时间'
    }
  },

  /**
   * 查看费用详情
   */
  viewExpenseDetail(event) {
    const { record } = event.currentTarget.dataset
    // 这里可以跳转到费用详情页面或显示详情弹窗
    wx.showModal({
      title: '费用详情',
      content: `描述：${record.description}\n分类：${record.category}\n金额：¥${record.amount}\n时间：${record.fullDate}${record.location ? '\n地点：' + record.location : ''}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * {{ AURA-X: Modify - 使用统一删除处理器，提升安卓兼容性. Approval: 寸止(ID:1738056000). }}
   * 删除费用记录
   */
  async deleteExpenseRecord(event) {
    const { record } = event.currentTarget.dataset
    if (!record || !record.id) {
      wx.showToast({
        title: '记录信息错误',
        icon: 'none'
      })
      return
    }

    try {
      // 使用统一删除处理器
      const unifiedDeleteHandler = require('../../../utils/unified-delete-handler.js').default

      const result = await unifiedDeleteHandler.deleteExpenseRecord(record.id, {
        confirmContent: `确定要删除这条费用记录吗？\n${record.description} ¥${record.amount}`,
        onSuccess: async () => {
          // 重新加载费用记录
          await this.loadExpenseRecords()
        },
        onError: (error) => {
          console.error('删除费用记录失败:', error)
        }
      })

      // 如果用户取消删除，不需要额外处理
      if (result.cancelled) {
        return
      }

    } catch (error) {
      console.error('删除费用记录异常:', error)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 添加新费用
   */
  addExpense() {
    wx.navigateTo({
      url: `/subpackages/account/travel-expense/index?mode=travel&planId=${this.data.planId}`
    })
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadExpenseRecords().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})
