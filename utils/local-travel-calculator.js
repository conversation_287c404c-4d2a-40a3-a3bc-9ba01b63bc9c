/**
 * 本地旅行数据计算引擎
 * 实现本地计算，减少云函数调用
 */

class LocalTravelCalculator {
  constructor() {
    // 计算缓存
    this.calculationCache = new Map()
    
    // 缓存有效期：1分钟
    this.cacheTimeout = 60 * 1000
  }

  /**
   * 计算旅行计划状态
   * @param {Object} plan 旅行计划
   * @returns {string} 状态：planning/ongoing/completed
   */
  calculatePlanStatus(plan) {
    if (!plan.startDate) return 'planning'
    
    const today = new Date()
    const startDate = new Date(plan.startDate)
    const endDate = new Date(plan.endDate)
    
    // 清除时间部分，只比较日期
    today.setHours(0, 0, 0, 0)
    startDate.setHours(0, 0, 0, 0)
    endDate.setHours(0, 0, 0, 0)
    
    if (today < startDate) return 'planning'
    if (today >= startDate && today <= endDate) return 'ongoing'
    return 'completed'
  }

  /**
   * 计算旅行统计数据
   * @param {Array} plans 旅行计划列表
   * @param {Array} expenses 支出记录列表
   * @returns {Object} 统计数据
   */
  calculateStatistics(plans, expenses = []) {
    const cacheKey = `stats_${plans.length}_${expenses.length}_${Date.now()}`
    
    // 检查缓存
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      return cached
    }

    const stats = {
      totalPlans: plans.length,
      completedPlans: 0,
      ongoingPlans: 0,
      plannedPlans: 0,
      totalExpense: 0,
      thisMonthExpense: 0,
      avgExpensePerTrip: 0,
      popularDestinations: []
    }

    const today = new Date()
    const thisMonth = today.getMonth()
    const thisYear = today.getFullYear()
    const destinationCount = {}

    // 计算计划统计
    plans.forEach(plan => {
      const status = this.calculatePlanStatus(plan)
      switch (status) {
        case 'completed': 
          stats.completedPlans++
          break
        case 'ongoing': 
          stats.ongoingPlans++
          break
        case 'planning': 
          stats.plannedPlans++
          break
      }

      // 统计热门目的地
      if (plan.destination) {
        destinationCount[plan.destination] = (destinationCount[plan.destination] || 0) + 1
      }
    })

    // 计算支出统计
    expenses.forEach(expense => {
      const amount = expense.amount || 0
      stats.totalExpense += amount

      const expenseDate = new Date(expense.createTime || expense.date)
      if (expenseDate.getMonth() === thisMonth && expenseDate.getFullYear() === thisYear) {
        stats.thisMonthExpense += amount
      }
    })

    // 计算平均支出
    if (stats.completedPlans > 0) {
      stats.avgExpensePerTrip = stats.totalExpense / stats.completedPlans
    }

    // 计算热门目的地
    stats.popularDestinations = Object.entries(destinationCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([destination, count]) => ({ destination, count }))

    // 数据清理
    stats.totalExpense = Math.round(stats.totalExpense * 100) / 100
    stats.thisMonthExpense = Math.round(stats.thisMonthExpense * 100) / 100
    stats.avgExpensePerTrip = Math.round(stats.avgExpensePerTrip * 100) / 100

    // 缓存结果
    this.setCache(cacheKey, stats)

    return stats
  }

  /**
   * 计算当前计划
   * @param {Array} plans 旅行计划列表
   * @returns {Object|null} 当前计划
   */
  calculateCurrentPlan(plans) {
    const ongoingPlans = plans.filter(plan => 
      this.calculatePlanStatus(plan) === 'ongoing'
    )

    if (ongoingPlans.length === 0) {
      return null
    }

    // 返回最早开始的进行中计划
    return ongoingPlans.sort((a, b) => 
      new Date(a.startDate) - new Date(b.startDate)
    )[0]
  }

  /**
   * 计算进行中的计划
   * @param {Array} plans 旅行计划列表
   * @returns {Array} 进行中的计划列表
   */
  calculateOngoingPlans(plans) {
    return plans
      .filter(plan => this.calculatePlanStatus(plan) === 'ongoing')
      .sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
  }

  /**
   * 过滤计划按状态
   * @param {Array} plans 旅行计划列表
   * @param {string} status 状态
   * @returns {Array} 过滤后的计划列表
   */
  filterPlansByStatus(plans, status) {
    return plans.filter(plan => this.calculatePlanStatus(plan) === status)
  }

  /**
   * 计算预算使用情况
   * @param {number} totalExpense 总支出
   * @param {number} budget 预算
   * @returns {Object} 预算使用情况
   */
  calculateBudgetUsage(totalExpense, budget) {
    if (!budget || budget <= 0) {
      return {
        usage: 0,
        remaining: 0,
        isOverBudget: false
      }
    }

    const usage = (totalExpense / budget) * 100
    const remaining = Math.max(0, budget - totalExpense)
    const isOverBudget = totalExpense > budget

    return {
      usage: Math.round(usage * 100) / 100,
      remaining: Math.round(remaining * 100) / 100,
      isOverBudget
    }
  }

  /**
   * 缓存管理
   */
  getFromCache(key) {
    const cached = this.calculationCache.get(key)
    if (!cached) return null

    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.calculationCache.delete(key)
      return null
    }

    return cached.data
  }

  setCache(key, data) {
    // 限制缓存大小
    if (this.calculationCache.size > 50) {
      const firstKey = this.calculationCache.keys().next().value
      this.calculationCache.delete(firstKey)
    }

    this.calculationCache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.calculationCache.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.calculationCache.size,
      timeout: this.cacheTimeout
    }
  }
}

// 创建全局实例
const localTravelCalculator = new LocalTravelCalculator()

export default localTravelCalculator
export { LocalTravelCalculator }
