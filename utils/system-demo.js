/**
 * 极简主义数据管理系统演示
 * 展示乐观更新、智能头像管理、分层同步的效果
 */

import optimisticUpdateManager from './optimistic-update-manager.js'
import layeredSyncManager from './layered-sync-manager.js'
import simpleDataManager from './simple-data-manager.js'

class SystemDemo {
  constructor() {
    this.demoData = {
      records: [],
      users: [],
      plans: []
    }
  }

  /**
   * 演示乐观更新效果
   */
  async demoOptimisticUpdate() {
    console.log('=== 乐观更新演示 ===')
    
    // 模拟添加记账记录
    const recordData = {
      amount: 100,
      category: '餐饮',
      description: '午餐',
      date: new Date().toISOString()
    }

    // UI更新函数 - 立即更新界面
    const uiUpdateFn = async (data) => {
      console.log('✅ UI立即更新:', data.description)
      this.demoData.records.push({ ...data, id: Date.now() })
      return null
    }

    // 同步函数 - 后台同步到云端
    const syncFn = async (data) => {
      console.log('🔄 后台同步中...', data.description)
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      console.log('✅ 同步成功:', data.description)
      return { success: true }
    }

    // 执行乐观更新
    const result = await optimisticUpdateManager.optimisticUpdate(
      'addRecord',
      recordData,
      uiUpdateFn,
      syncFn
    )

    console.log('乐观更新结果:', result)
    console.log('当前记录数:', this.demoData.records.length)
  }

  /**
   * 演示头像管理（已简化）
   */
  async demoAvatarManagement() {
    console.log('\n=== 头像管理演示（已简化） ===')

    const fileID = 'cloud://test-avatar.jpg'

    // 直接使用云存储文件ID
    console.log('头像文件ID:', fileID)
    console.log('头像管理已简化，直接使用云存储链接')
  }

  /**
   * 演示分层同步
   */
  async demoLayeredSync() {
    console.log('\n=== 分层同步演示 ===')

    // 关键操作 - 立即同步
    console.log('1. 关键操作演示（删除记录）')
    await this.demoSyncLayer('deleteRecord', { id: 123 }, 'critical')

    // 重要操作 - 延迟同步
    console.log('2. 重要操作演示（添加记录）')
    await this.demoSyncLayer('addRecord', { amount: 50 }, 'important')

    // 一般操作 - 批量同步
    console.log('3. 一般操作演示（更新统计）')
    await this.demoSyncLayer('updateStatistics', { views: 1 }, 'general')

    // 显示同步状态
    setTimeout(() => {
      const status = layeredSyncManager.getSyncStatus()
      console.log('同步状态:', status)
    }, 5000)
  }

  async demoSyncLayer(action, data, expectedCategory) {
    const uiUpdateFn = async (data) => {
      console.log(`✅ ${action} UI更新完成`)
      return null
    }

    const syncFn = async (data) => {
      console.log(`🔄 ${action} 后台同步中...`)
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log(`✅ ${action} 同步完成`)
      return { success: true }
    }

    await layeredSyncManager.layeredSync(action, data, syncFn, uiUpdateFn)
  }

  /**
   * 演示简化数据管理器
   */
  async demoSimpleDataManager() {
    console.log('\n=== 简化数据管理器演示 ===')

    // 本地优先获取数据
    console.log('1. 本地优先获取数据')
    const result1 = await simpleDataManager.getData(
      'demo_data',
      async () => {
        console.log('🔄 云端获取数据...')
        await new Promise(resolve => setTimeout(resolve, 1000))
        return { success: true, data: { value: 'from_cloud' } }
      }
    )
    console.log('第一次获取:', result1)

    // 第二次获取 - 应该从缓存返回
    console.log('2. 缓存获取数据')
    const result2 = await simpleDataManager.getData('demo_data')
    console.log('第二次获取:', result2)

    // 本地计算演示
    console.log('3. 本地计算演示')
    const calculated = simpleDataManager.calculateLocally(
      'demo_calc',
      () => {
        console.log('执行本地计算...')
        return { sum: 1 + 2 + 3, timestamp: Date.now() }
      },
      ['dependency1', 'dependency2']
    )
    console.log('计算结果:', calculated)

    // 第二次计算 - 应该从缓存返回
    const calculated2 = simpleDataManager.calculateLocally(
      'demo_calc',
      () => {
        console.log('这不应该执行')
        return { sum: 999 }
      },
      ['dependency1', 'dependency2']
    )
    console.log('缓存计算结果:', calculated2)
  }

  /**
   * 性能对比演示
   */
  async demoPerformanceComparison() {
    console.log('\n=== 性能对比演示 ===')

    // 传统方式 - 每次都调用云函数
    console.log('传统方式:')
    const start1 = Date.now()
    for (let i = 0; i < 5; i++) {
      await this.simulateCloudFunction()
    }
    const time1 = Date.now() - start1
    console.log(`传统方式耗时: ${time1}ms`)

    // 新方式 - 本地优先 + 缓存
    console.log('新方式:')
    const start2 = Date.now()
    for (let i = 0; i < 5; i++) {
      await simpleDataManager.getData(
        `perf_test_${i}`,
        () => this.simulateCloudFunction(),
        { useCache: true }
      )
    }
    const time2 = Date.now() - start2
    console.log(`新方式耗时: ${time2}ms`)

    console.log(`性能提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`)
  }

  async simulateCloudFunction() {
    // 模拟云函数调用延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    return { success: true, data: { timestamp: Date.now() } }
  }

  /**
   * 运行完整演示
   */
  async runFullDemo() {
    console.log('🚀 极简主义数据管理系统演示开始')
    console.log('=====================================')

    try {
      await this.demoOptimisticUpdate()
      await this.demoSmartAvatarManager()
      await this.demoLayeredSync()
      await this.demoSimpleDataManager()
      await this.demoPerformanceComparison()

      console.log('\n=====================================')
      console.log('✅ 演示完成！')
      
      // 显示系统状态
      setTimeout(() => {
        console.log('\n=== 系统状态 ===')
        console.log('乐观更新状态:', optimisticUpdateManager.getSyncStatus())
        console.log('分层同步状态:', layeredSyncManager.getSyncStatus())
        console.log('缓存状态:', simpleDataManager.getCacheStatus())
      }, 6000)

    } catch (error) {
      console.error('演示过程中出错:', error)
    }
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats() {
    return {
      optimisticUpdate: optimisticUpdateManager.getSyncStatus(),
      layeredSync: layeredSyncManager.getSyncStatus(),
      dataManager: simpleDataManager.getCacheStatus(),
      timestamp: new Date().toISOString()
    }
  }
}

// 创建演示实例
const systemDemo = new SystemDemo()

// 导出演示函数，可在控制台调用
if (typeof window !== 'undefined') {
  window.runSystemDemo = () => systemDemo.runFullDemo()
  window.getSystemStats = () => systemDemo.getSystemStats()
}

export default systemDemo
export { SystemDemo }
